import React, { useState } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { AiOutlineLoading } from 'react-icons/ai';
import { IoMdAdd, IoMdSettings } from 'react-icons/io';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectSelectedChatType,
  selectPolicyLocationOptions,
  selectSelectedPolicyLocation,
  setSelectedPolicyLocation,
  selectSelectedTemperature,
  setSelectedTemperature,
} from '@/store/slices/chatSlice';
import PolicyLocationDropdown from '../../policy_web/components/PolicyLocationDropdown';
import { shouldShowFileUploadUI } from './fileUpload/utils/fileUploadHelpers';
import { TemperatureIcon } from '@/components/common/icons';
import ChatSettingsModal, { getChatTemperatureDisplayName } from './ChatSettingsModal';

// const temperatureOptions = [
//   { label: 'Less Creative', value: 0.2 },
//   { label: 'Classic', value: 1.0 },
//   { label: 'More Creative', value: 1.8 },
// ];

interface InputLowerTrayProps {
  onSubmit: () => void;
  onAttachFileClick?: () => void;
  isSendingMessage?: boolean;
  disableSubmit?: boolean;
}

/**
 * InputLowerTray component provides UI elements for chat input,
 * including file attachment and message submission.
 */
const InputLowerTray: React.FC<InputLowerTrayProps> = ({
  onSubmit,
  onAttachFileClick,
  isSendingMessage = false,
  disableSubmit = false,
}) => {
  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const policyLocationOptions = useAppSelector(selectPolicyLocationOptions);
  const selectedPolicyLocation = useAppSelector(selectSelectedPolicyLocation);
  const selectedTemperature = useAppSelector(selectSelectedTemperature);

  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  const handleSettingsClick = () => {
    setIsSettingsModalOpen(true);
  };

  const handleSettingsModalClose = () => {
    setIsSettingsModalOpen(false);
  };

  const handleTemperatureSave = (temperature: number) => {
    dispatch(setSelectedTemperature(temperature));
  };

  const handlePolicyLocationChange = (value: string) => {
    dispatch(setSelectedPolicyLocation(value));
  };

  const showAttach = shouldShowFileUploadUI(selectedChatType);

  return (
    <>
      <div className="chat-input-lower-tray flex w-full items-center" style={{ justifyContent: 'space-between' }}>
        {/* Left side: Policy dropdown or Attach file button */}
        <div className="chat-input-lower-tray__left-section flex items-center">
          {selectedChatType === 'Policy' ? (
            <div className="chat-input-lower-tray__policy-dropdown-container flex-shrink-0 w-auto min-w-[200px] max-w-[320px]">
              <PolicyLocationDropdown
                options={policyLocationOptions}
                selectedValue={selectedPolicyLocation}
                onChange={handlePolicyLocationChange}
                disabled={isSendingMessage}
                className=""
              />
            </div>
          ) : showAttach ? (
            // Attach file button positioned on the left - no background, white text and icon
            <button
              type="button"
              className="flex items-center gap-[8px] text-center font-roboto text-[16px] font-semibold leading-tight transition-all duration-200 ease-out hover:bg-[#0066B1] hover:bg-opacity-10 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#0066B1] active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-[50px] border-[1.5px] border-solid border-[#0066B1] px-3 py-2"
              aria-label="Attach file"
              onClick={onAttachFileClick}
              title="Click to attach files"
              disabled={isSendingMessage}
            >
              <IoMdAdd className="w-6 h-6 text-white" />
              <span className="text-white">Attach file</span>
            </button>
          ) : (
            // Empty placeholder to maintain layout when no attach button
            <div></div>
          )}
        </div>

        {/* Right side: Temperature, Settings, and Send button */}
        <div className="chat-input-lower-tray__actions flex items-center gap-2">
          {/* Temperature Badge - Always visible for non-Policy modes */}
          {selectedChatType !== 'Policy' && (
            <div className="flex items-center gap-[8px] px-3 py-2 rounded-[50px] border-[1.5px] border-solid border-[#0066B1] text-white">
              <TemperatureIcon className="w-5 h-5" />
              <span className="text-[14px] font-roboto font-medium whitespace-nowrap">
                {getChatTemperatureDisplayName(selectedTemperature)}
              </span>
            </div>
          )}

          {/* Settings Button - Only visible for non-Policy modes */}
          {selectedChatType !== 'Policy' && (
            <button
              type="button"
              className="p-2 rounded-[50px] border-[1.5px] border-solid border-[#0066B1] text-white hover:opacity-80 transition-all active:scale-95 active:opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 font-roboto"
              style={{ borderColor: '#0066B1' }}
              onClick={handleSettingsClick}
              aria-label="Settings"
              disabled={isSendingMessage}
            >
              <IoMdSettings className="h-5 w-5" />
            </button>
          )}

          {/* Send Button */}
          <button
            type="button"
            onClick={onSubmit}
            className="p-2 rounded-[50px] border-[1.5px] border-solid border-[#0066B1] text-white hover:opacity-80 transition-all active:scale-95 active:opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 font-roboto disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ borderColor: '#0066B1' }}
            aria-label="Send message"
            disabled={isSendingMessage || disableSubmit}
          >
            {isSendingMessage ? (
              <AiOutlineLoading className="h-6 w-6 animate-spin" />
            ) : (
              <FaArrowRight className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      <ChatSettingsModal
        isOpen={isSettingsModalOpen}
        onClose={handleSettingsModalClose}
        onSave={handleTemperatureSave}
        currentTemperature={selectedTemperature}
      />
    </>
  );
};

export default InputLowerTray;
