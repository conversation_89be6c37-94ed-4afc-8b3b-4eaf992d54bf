import React from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { AiOutlineLoading } from 'react-icons/ai';
import { IoMdAdd } from 'react-icons/io';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectSelectedChatType,
  selectPolicyLocationOptions,
  selectSelectedPolicyLocation,
  setSelectedPolicyLocation,
  // selectSelectedTemperature,
  // setSelectedTemperature,
} from '@/store/slices/chatSlice';
import PolicyLocationDropdown from '../../policy_web/components/PolicyLocationDropdown';
import { shouldShowFileUploadUI } from './fileUpload/utils/fileUploadHelpers';

// const temperatureOptions = [
//   { label: 'Less Creative', value: 0.2 },
//   { label: 'Classic', value: 1.0 },
//   { label: 'More Creative', value: 1.8 },
// ];

interface InputLowerTrayProps {
  onSubmit: () => void;
  onAttachFileClick?: () => void;
  isSendingMessage?: boolean;
  disableSubmit?: boolean;
}

/**
 * InputLowerTray component provides UI elements for chat input,
 * including file attachment and message submission.
 */
const InputLowerTray: React.FC<InputLowerTrayProps> = ({
  onSubmit,
  onAttachFileClick,
  isSendingMessage = false,
  disableSubmit = false,
}) => {
  const { classes } = useThemeStyles();
  const block = 'input-lower-tray';

  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const policyLocationOptions = useAppSelector(selectPolicyLocationOptions);
  const selectedPolicyLocation = useAppSelector(selectSelectedPolicyLocation);
  // const selectedTemperature = useAppSelector(selectSelectedTemperature);

  // const [localTemperature, setLocalTemperature] = useState(selectedTemperature);

  // useEffect(() => {
  //   if (selectedChatType !== 'Policy') {
  //     setLocalTemperature(selectedTemperature);
  //   }
  // }, [selectedTemperature, selectedChatType]);

  // Keep temperature functionality but hidden UI
  // const onTempClick = (value: number) => {
  //   if (selectedChatType !== 'Policy') {
  //     setLocalTemperature(value);
  //     dispatch(setSelectedTemperature(value));
  //   }
  // };

  const handlePolicyLocationChange = (value: string) => {
    dispatch(setSelectedPolicyLocation(value));
  };

  const showAttach = shouldShowFileUploadUI(selectedChatType);

  return (
    <div className="input-lower-tray flex w-full items-center justify-between">
      {selectedChatType === 'Policy' ? (
        <div className="input-lower-tray__policy-dropdown-container flex-shrink-0 w-auto min-w-[200px] max-w-[320px]">
          <PolicyLocationDropdown
            options={policyLocationOptions}
            selectedValue={selectedPolicyLocation}
            onChange={handlePolicyLocationChange}
            disabled={isSendingMessage}
            className=""
          />
        </div>
      ) : (
        // Attach file button positioned on the left
        <div className="input-lower-tray__attach-file-container">
          {showAttach && (
            <button
              type="button"
              className="flex items-center gap-[8px] text-center font-roboto text-[16px] font-semibold leading-tight transition-all duration-200 ease-out hover:bg-[#0066B1] hover:text-white focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#0066B1] active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed text-white bg-[#0066B1] rounded-[50px] border-[1.5px] border-solid border-[#0066B1] px-3 py-2"
              aria-label="Attach file"
              onClick={onAttachFileClick}
              title="Click to attach files"
              disabled={isSendingMessage}
            >
              <IoMdAdd className="w-6 h-6 text-white" />
              <span className="text-white">Attach file</span>
            </button>
          )}
        </div>
      )}

      {/* Action buttons: Send message only */}
      <div className={`${block}__actions flex items-center gap-2`}>
        <button
          type="button"
          onClick={onSubmit}
          className="p-2 rounded-[50px] border-[1.5px] border-solid border-[#0066B1] text-[#003963] hover:opacity-80 transition-all active:scale-95 active:opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 font-roboto disabled:border-gray-300 disabled:text-gray-300 disabled:cursor-not-allowed"
          aria-label="Send message"
          disabled={isSendingMessage || disableSubmit}
        >
          {isSendingMessage ? (
            <AiOutlineLoading className={`${block}__send-icon ${block}__send-icon--loading h-5 w-5 animate-spin`} />
          ) : (
            <FaArrowRight className={`${block}__send-icon h-5 w-5`} />
          )}
        </button>
      </div>
    </div>
  );
};

export default InputLowerTray;
