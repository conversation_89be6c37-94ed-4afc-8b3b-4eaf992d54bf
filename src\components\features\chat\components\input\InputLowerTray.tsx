import React, { useState } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { AiOutlineLoading } from 'react-icons/ai';
import { IoMdAdd, IoMdSettings } from 'react-icons/io';
import clsx from 'clsx';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  selectSelectedChatType,
  selectPolicyLocationOptions,
  selectSelectedPolicyLocation,
  setSelectedPolicyLocation,
  selectSelectedTemperature,
  setSelectedTemperature,
} from '@/store/slices/chatSlice';
import PolicyLocationDropdown from '../../../policy_web/components/PolicyLocationDropdown';
import { shouldShowFileUploadUI } from '../fileUpload/utils/fileUploadHelpers';
import { TemperatureIcon } from '@/components/common/icons';
import ChatSettingsModal, { getChatTemperatureDisplayName } from '../modal/ChatSettingsModal';

interface InputLowerTrayProps {
  onSubmit: () => void;
  onAttachFileClick?: () => void;
  isSendingMessage?: boolean;
  disableSubmit?: boolean;
}

/**
 * InputLowerTray component provides UI elements for chat input,
 * including file attachment and message submission.
 */
const InputLowerTray: React.FC<InputLowerTrayProps> = ({
  onSubmit,
  onAttachFileClick,
  isSendingMessage = false,
  disableSubmit = false,
}) => {
  const dispatch = useAppDispatch();
  const selectedChatType = useAppSelector(selectSelectedChatType);
  const policyLocationOptions = useAppSelector(selectPolicyLocationOptions);
  const selectedPolicyLocation = useAppSelector(selectSelectedPolicyLocation);
  const selectedTemperature = useAppSelector(selectSelectedTemperature);

  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);

  // BEM base class
  const block = 'input-lower-tray';

  // Reusable style combinations
  const buttonBaseClasses = [
    'flex items-center gap-2',
    'px-3 py-2',
    'rounded-[50px]',
    'border-[1.5px] border-solid border-[#0066B1]',
    'text-white font-roboto',
    'transition-all duration-200',
    'hover:opacity-80 active:scale-95',
    'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500',
  ].join(' ');

  const iconButtonClasses = [
    'p-2',
    'rounded-[50px]',
    'border-[1.5px] border-solid border-[#0066B1]',
    'text-white font-roboto',
    'transition-all duration-200',
    'hover:opacity-80 active:scale-95',
    'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500',
  ].join(' ');

  const attachButtonClasses = clsx(
    buttonBaseClasses,
    'text-[16px] font-semibold',
    'hover:bg-[#0066B1] hover:bg-opacity-10',
    'disabled:opacity-50 disabled:cursor-not-allowed'
  );

  const handleSettingsClick = () => {
    setIsSettingsModalOpen(true);
  };

  const handleSettingsModalClose = () => {
    setIsSettingsModalOpen(false);
  };

  const handleTemperatureSave = (temperature: number) => {
    dispatch(setSelectedTemperature(temperature));
  };

  const handlePolicyLocationChange = (value: string) => {
    dispatch(setSelectedPolicyLocation(value));
  };

  const showAttach = shouldShowFileUploadUI(selectedChatType);

  return (
    <>
      <div className={clsx(block, 'flex w-full items-center')}>
        {/* Left Section - Always at the far left */}
        <div className={clsx(`${block}__left-section`, 'flex items-center')}>
          {selectedChatType === 'Policy' ? (
            <div className={clsx(`${block}__policy-dropdown`, 'flex-shrink-0 w-auto min-w-[200px] max-w-[320px]')}>
              <PolicyLocationDropdown
                options={policyLocationOptions}
                selectedValue={selectedPolicyLocation}
                onChange={handlePolicyLocationChange}
                disabled={isSendingMessage}
                className=""
              />
            </div>
          ) : showAttach ? (
            <button
              type="button"
              className={clsx(`${block}__attach-btn`, attachButtonClasses)}
              onClick={onAttachFileClick}
              disabled={isSendingMessage}
              aria-label="Attach file"
              title="Click to attach files"
            >
              <IoMdAdd className="w-6 h-6" />
              <span>Attach file</span>
            </button>
          ) : null}
        </div>

        {/* Spacer to push right section to the far right */}
        <div className="flex-1" />

        {/* Right Section - Always at the far right */}
        <div className={clsx(`${block}__actions`, 'flex items-center gap-2')}>
          {/* Temperature Badge - No radius, no border */}
          {selectedChatType !== 'Policy' && (
            <div
              className={clsx(
                `${block}__temperature-badge`,
                'flex items-center gap-2',
                'text-white text-sm font-roboto font-medium whitespace-nowrap'
              )}
            >
              <TemperatureIcon className="w-5 h-5" />
              <span>{getChatTemperatureDisplayName(selectedTemperature)}</span>
            </div>
          )}

          {/* Settings Button */}
          {selectedChatType !== 'Policy' && (
            <button
              type="button"
              className={clsx(`${block}__settings-btn`, iconButtonClasses)}
              onClick={handleSettingsClick}
              disabled={isSendingMessage}
              aria-label="Settings"
            >
              <IoMdSettings className="w-6 h-6" />
            </button>
          )}

          {/* Send Button */}
          <button
            type="button"
            className={clsx(`${block}__send-btn`, iconButtonClasses, 'disabled:opacity-50 disabled:cursor-not-allowed')}
            onClick={onSubmit}
            disabled={isSendingMessage || disableSubmit}
            aria-label="Send message"
          >
            {isSendingMessage ? (
              <AiOutlineLoading className="w-6 h-6 animate-spin" />
            ) : (
              <FaArrowRight className="w-6 h-6" />
            )}
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      <ChatSettingsModal
        isOpen={isSettingsModalOpen}
        onClose={handleSettingsModalClose}
        onSave={handleTemperatureSave}
        currentTemperature={selectedTemperature}
      />
    </>
  );
};

export default InputLowerTray;
